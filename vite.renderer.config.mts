import { defineConfig } from "vite";
import * as path from "path";
import react from "@vitejs/plugin-react";

import tailwindcss from "@tailwindcss/vite";
import { tanstackRouter } from "@tanstack/router-plugin/vite";

// https://vitejs.dev/config
export default defineConfig({
  plugins: [
    // tanstackRouter({
    //   target: "react",
    //   autoCodeSplitting: true,
    // }),
    tailwindcss(),
    react(),
  ],
  resolve: {
    preserveSymlinks: true,
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
