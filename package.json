{"name": "desktop", "productName": "desktop", "version": "1.0.0", "description": "My Electron application description", "main": ".vite/build/main.js", "private": true, "scripts": {"dev": "electron-forge start", "start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx ."}, "keywords": [], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "pnpm": {"onlyBuiltDependencies": ["electron", "electron-winstaller"], "ignoredBuiltDependencies": ["@tailwindcss/oxide", "esbuild"]}, "devDependencies": {"@electron-forge/cli": "^7.8.3", "@electron-forge/maker-deb": "^7.8.3", "@electron-forge/maker-rpm": "^7.8.3", "@electron-forge/maker-squirrel": "^7.8.3", "@electron-forge/maker-zip": "^7.8.3", "@electron-forge/plugin-auto-unpack-natives": "^7.8.3", "@electron-forge/plugin-fuses": "^7.8.3", "@electron-forge/plugin-vite": "^7.8.3", "@electron/fuses": "^1.8.0", "@tanstack/router-plugin": "^1.131.27", "@types/electron-squirrel-startup": "^1.0.2", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "electron": "37.3.1", "eslint": "^8.57.1", "eslint-plugin-import": "^2.32.0", "typescript": "~4.5.5", "vite": "^5.4.19"}, "dependencies": {"@excalidraw/excalidraw": "^0.18.0", "@tailwindcss/vite": "^4.1.12", "@tanstack/react-router": "^1.131.27", "@tanstack/react-router-devtools": "^1.131.27", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.1", "electron-squirrel-startup": "^1.0.1", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwindcss": "^4.1.12"}}