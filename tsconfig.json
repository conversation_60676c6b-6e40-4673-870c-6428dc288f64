{"compilerOptions": {"jsx": "react-jsx", "target": "ESNext", "module": "ESNext", "lib": ["dom", "ESNext"], "experimentalDecorators": true, "composite": true, "declaration": true, "forceConsistentCasingInFileNames": true, "allowJs": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "noImplicitAny": true, "sourceMap": true, "strict": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "outDir": "dist", "moduleResolution": "bundler", "resolveJsonModule": true}, "include": ["src/**/*", "./package.json", "./forge.config.ts", "*.mts", "vite.renderer.config.mts", "vite.main.config.mts", "vite.preload.config.mts"]}